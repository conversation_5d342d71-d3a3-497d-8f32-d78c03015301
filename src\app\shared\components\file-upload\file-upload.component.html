<!-- Upload Box -->
<div class="upload-container" [ngClass]="(hasUploadedFiles() && !multiple) || !canUploadFiles() ? 'disabled' : ''" (drop)="onDrop($event)"
    (dragover)="onDragOver($event)" (click)="((!hasUploadedFiles() || multiple) && canUploadFiles()) && fileInput.click()">
    <div class="upload-content">
        <img src="assets/images/file.png" alt="upload icon" />
        <p class="upload-text">{{ 'FILE_UPLOAD.DRAG_DROP_TEXT' | translate }}</p>
        <p class="subtext">
            {{ 'FILE_UPLOAD.MAX_SIZE_TEXT' | translate: {maxSize: maxSize} }}، {{ 'FILE_UPLOAD.SUPPORTED_FORMATS' |
            translate }}
            <strong>{{ getSupportedFormatsText() }}</strong>
            <span *ngIf="multiple" class="max-files-text">
                {{ 'FILE_UPLOAD.MAX_FILES' | translate: {maxFiles: 10} }}
            </span>
        </p>
        <input type="file" [accept]="getAcceptAttribute()" (change)="onFileSelected($event)" hidden #fileInput
            [disabled]="(hasUploadedFiles() && !multiple) || !canUploadFiles()" [multiple]="multiple" />
    </div>
</div>

<div *ngIf="multiple && uploadedFiles.length > 0" class="mt-2">
    <div class="d-flex wrap gap-2">
        <div *ngFor="let file of uploadedFiles; let i = index" class="file-info tag d-flex align-items-center gap-2">
            <span class="mx-1" (click)="downloadFile(file.url || file.filePath, file.fileName || file.name)"
                title="تحميل الملف">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M0.842105 10.9482C1.06544 10.9482 1.27964 11.037 1.43756 11.1949C1.59549 11.3528 1.68421 11.567 1.68421 11.7903V13.4746C1.68421 13.6979 1.77293 13.9121 1.93086 14.07C2.08878 14.2279 2.30297 14.3167 2.52631 14.3167H12.6316C12.8549 14.3167 13.0691 14.2279 13.227 14.07C13.385 13.9121 13.4737 13.6979 13.4737 13.4746V11.7903C13.4737 11.567 13.5624 11.3528 13.7203 11.1949C13.8782 11.037 14.0924 10.9482 14.3158 10.9482C14.5391 10.9482 14.7533 11.037 14.9112 11.1949C15.0692 11.3528 15.1579 11.567 15.1579 11.7903V13.4746C15.1579 14.1446 14.8917 14.7872 14.4179 15.2609C13.9442 15.7347 13.3016 16.0009 12.6316 16.0009H2.52631C1.85629 16.0009 1.21372 15.7347 0.73994 15.2609C0.266165 14.7872 0 14.1446 0 13.4746V11.7903C0 11.567 0.0887215 11.3528 0.246647 11.1949C0.404572 11.037 0.618765 10.9482 0.842105 10.9482Z"
                        fill="#00205A" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M2.77292 6.14283C2.93084 5.98496 3.145 5.89627 3.36829 5.89627C3.59159 5.89627 3.80574 5.98496 3.96366 6.14283L7.57882 9.75798L11.194 6.14283C11.2717 6.0624 11.3646 5.99824 11.4673 5.95411C11.5701 5.90998 11.6806 5.88675 11.7924 5.88577C11.9042 5.8848 12.0151 5.90611 12.1186 5.94845C12.2221 5.99079 12.3161 6.05332 12.3951 6.13239C12.4742 6.21146 12.5367 6.30548 12.5791 6.40897C12.6214 6.51246 12.6427 6.62335 12.6418 6.73516C12.6408 6.84698 12.6176 6.95748 12.5734 7.06022C12.5293 7.16296 12.4651 7.25588 12.3847 7.33356L8.17418 11.5441C8.01627 11.702 7.80211 11.7906 7.57882 11.7906C7.35552 11.7906 7.14137 11.702 6.98345 11.5441L2.77292 7.33356C2.61505 7.17565 2.52637 6.96149 2.52637 6.7382C2.52637 6.5149 2.61505 6.30075 2.77292 6.14283Z"
                        fill="#00205A" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M7.57892 0.000976562C7.80226 0.000976563 8.01645 0.089698 8.17438 0.247623C8.3323 0.405549 8.42103 0.619741 8.42103 0.843081V10.9483C8.42103 11.1717 8.3323 11.3859 8.17438 11.5438C8.01645 11.7017 7.80226 11.7904 7.57892 11.7904C7.35558 11.7904 7.14139 11.7017 6.98346 11.5438C6.82554 11.3859 6.73682 11.1717 6.73682 10.9483V0.843081C6.73682 0.619741 6.82554 0.405549 6.98346 0.247623C7.14139 0.089698 7.35558 0.000976563 7.57892 0.000976562Z"
                        fill="#00205A" />
                </svg>
            </span>
            <span class="mx-1">{{ file.fileName }}</span>
            <span class="mx-1 text-danger" (click)="removeFile(i)" title="حذف الملف">

                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M3.20895 3.21885C3.34279 3.08506 3.52428 3.0099 3.71352 3.0099C3.90276 3.0099 4.08425 3.08506 4.21808 3.21885L7.99554 6.99631L11.773 3.21885C11.8388 3.15069 11.9176 3.09632 12.0046 3.05892C12.0917 3.02151 12.1854 3.00183 12.2801 3.001C12.3749 3.00018 12.4689 3.01824 12.5566 3.05412C12.6443 3.09 12.724 3.143 12.791 3.21001C12.858 3.27701 12.911 3.3567 12.9469 3.4444C12.9827 3.53211 13.0008 3.62609 13 3.72085C12.9991 3.81561 12.9795 3.90926 12.9421 3.99633C12.9047 4.0834 12.8503 4.16215 12.7821 4.22798L9.00467 8.00544L12.7821 11.7829C12.9121 11.9175 12.9841 12.0978 12.9824 12.2849C12.9808 12.472 12.9058 12.651 12.7734 12.7833C12.6411 12.9156 12.4621 12.9907 12.275 12.9923C12.0879 12.994 11.9076 12.922 11.773 12.792L7.99554 9.01457L4.21808 12.792C4.08348 12.922 3.90321 12.994 3.71609 12.9923C3.52896 12.9907 3.34997 12.9156 3.21765 12.7833C3.08533 12.651 3.01027 12.472 3.00864 12.2849C3.00702 12.0978 3.07895 11.9175 3.20895 11.7829L6.98641 8.00544L3.20895 4.22798C3.07516 4.09415 3 3.91266 3 3.72342C3 3.53418 3.07516 3.35268 3.20895 3.21885Z"
                        fill="#4F4F4F" />
                </svg>
            </span>
        </div>
    </div>
</div>

<div *ngIf="!multiple && fileUrl" class="d-flex wrap align-items-center gap-2 mt-2">
    <div class="file-info tag">

        <div class="file-actions">


            <span class="mx-1" (click)="downloadFile(fileUrl, fileName ||'')" title="تحميل الملف">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M0.842105 10.9482C1.06544 10.9482 1.27964 11.037 1.43756 11.1949C1.59549 11.3528 1.68421 11.567 1.68421 11.7903V13.4746C1.68421 13.6979 1.77293 13.9121 1.93086 14.07C2.08878 14.2279 2.30297 14.3167 2.52631 14.3167H12.6316C12.8549 14.3167 13.0691 14.2279 13.227 14.07C13.385 13.9121 13.4737 13.6979 13.4737 13.4746V11.7903C13.4737 11.567 13.5624 11.3528 13.7203 11.1949C13.8782 11.037 14.0924 10.9482 14.3158 10.9482C14.5391 10.9482 14.7533 11.037 14.9112 11.1949C15.0692 11.3528 15.1579 11.567 15.1579 11.7903V13.4746C15.1579 14.1446 14.8917 14.7872 14.4179 15.2609C13.9442 15.7347 13.3016 16.0009 12.6316 16.0009H2.52631C1.85629 16.0009 1.21372 15.7347 0.73994 15.2609C0.266165 14.7872 0 14.1446 0 13.4746V11.7903C0 11.567 0.0887215 11.3528 0.246647 11.1949C0.404572 11.037 0.618765 10.9482 0.842105 10.9482Z"
                        fill="#00205A" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M2.77292 6.14283C2.93084 5.98496 3.145 5.89627 3.36829 5.89627C3.59159 5.89627 3.80574 5.98496 3.96366 6.14283L7.57882 9.75798L11.194 6.14283C11.2717 6.0624 11.3646 5.99824 11.4673 5.95411C11.5701 5.90998 11.6806 5.88675 11.7924 5.88577C11.9042 5.8848 12.0151 5.90611 12.1186 5.94845C12.2221 5.99079 12.3161 6.05332 12.3951 6.13239C12.4742 6.21146 12.5367 6.30548 12.5791 6.40897C12.6214 6.51246 12.6427 6.62335 12.6418 6.73516C12.6408 6.84698 12.6176 6.95748 12.5734 7.06022C12.5293 7.16296 12.4651 7.25588 12.3847 7.33356L8.17418 11.5441C8.01627 11.702 7.80211 11.7906 7.57882 11.7906C7.35552 11.7906 7.14137 11.702 6.98345 11.5441L2.77292 7.33356C2.61505 7.17565 2.52637 6.96149 2.52637 6.7382C2.52637 6.5149 2.61505 6.30075 2.77292 6.14283Z"
                        fill="#00205A" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M7.57892 0.000976562C7.80226 0.000976563 8.01645 0.089698 8.17438 0.247623C8.3323 0.405549 8.42103 0.619741 8.42103 0.843081V10.9483C8.42103 11.1717 8.3323 11.3859 8.17438 11.5438C8.01645 11.7017 7.80226 11.7904 7.57892 11.7904C7.35558 11.7904 7.14139 11.7017 6.98346 11.5438C6.82554 11.3859 6.73682 11.1717 6.73682 10.9483V0.843081C6.73682 0.619741 6.82554 0.405549 6.98346 0.247623C7.14139 0.089698 7.35558 0.000976563 7.57892 0.000976562Z"
                        fill="#00205A" />
                </svg>
            </span>
            <span class="mx-1">{{ fileName }}</span>
            <span class="mx-1 text-danger" (click)="removeFile()" title="حذف الملف">

                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M3.20895 3.21885C3.34279 3.08506 3.52428 3.0099 3.71352 3.0099C3.90276 3.0099 4.08425 3.08506 4.21808 3.21885L7.99554 6.99631L11.773 3.21885C11.8388 3.15069 11.9176 3.09632 12.0046 3.05892C12.0917 3.02151 12.1854 3.00183 12.2801 3.001C12.3749 3.00018 12.4689 3.01824 12.5566 3.05412C12.6443 3.09 12.724 3.143 12.791 3.21001C12.858 3.27701 12.911 3.3567 12.9469 3.4444C12.9827 3.53211 13.0008 3.62609 13 3.72085C12.9991 3.81561 12.9795 3.90926 12.9421 3.99633C12.9047 4.0834 12.8503 4.16215 12.7821 4.22798L9.00467 8.00544L12.7821 11.7829C12.9121 11.9175 12.9841 12.0978 12.9824 12.2849C12.9808 12.472 12.9058 12.651 12.7734 12.7833C12.6411 12.9156 12.4621 12.9907 12.275 12.9923C12.0879 12.994 11.9076 12.922 11.773 12.792L7.99554 9.01457L4.21808 12.792C4.08348 12.922 3.90321 12.994 3.71609 12.9923C3.52896 12.9907 3.34997 12.9156 3.21765 12.7833C3.08533 12.651 3.01027 12.472 3.00864 12.2849C3.00702 12.0978 3.07895 11.9175 3.20895 11.7829L6.98641 8.00544L3.20895 4.22798C3.07516 4.09415 3 3.91266 3 3.72342C3 3.53418 3.07516 3.35268 3.20895 3.21885Z"
                        fill="#4F4F4F" />
                </svg>
            </span>
        </div>
    </div>
</div>
<p *ngIf="error" class="is-invalid">
    {{ error }}
</p>