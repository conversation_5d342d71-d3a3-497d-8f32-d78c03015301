import {
  HttpErrorResponse,
  HttpInterceptorFn
} from '@angular/common/http';
import { throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

export const errorInterceptor: HttpInterceptorFn = (req, next) => {
  return next(req).pipe(
    catchError((error: HttpErrorResponse) => {
      debugger;
      // ✅ Access status, headers, body, etc.
      console.log('Status:', error.status);
      console.log('Headers:', error.headers);
      console.log('Error body:', error.error);
      console.log('Error message:', error.message);
      console.log('URL:', error.url);

      // You can customize the error message returned
      let errorMsg = '';

      if (error.error && typeof error.error === 'object') {
        // If it's a structured object, extract meaningful info
        errorMsg = error.error.message || 'Unknown backend error';
      } else if (typeof error.error === 'string') {
        errorMsg = error.error;
      } else {
        errorMsg = 'An unexpected error occurred';
      }

      // Optionally show a toast or translate messages here

      return throwError(() => new Error(errorMsg));
    })
  );
};
