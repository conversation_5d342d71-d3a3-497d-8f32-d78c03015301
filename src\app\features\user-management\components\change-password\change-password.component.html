<!-- Change Password Section -->
<div class="change-password-section">
  <div class="section-header mb-4">
    <h4 class="section-title">{{ 'USER_PROFILE.CHANGE_PASSWORD' | translate }}</h4>
  </div>

    <!-- Form Fields -->

    <div class="form-fields-section">
      <app-form-builder
        [formControls]="formControls"
        [formGroup]="changePasswordForm"
        [isFormSubmitted]="isFormSubmitted">

        <div slot="between" class="hr-first-container">
                    <ul class="password-rules list-unstyled">
                        <li>{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_1' | translate }}</li>
                        <li>{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_2' | translate }}</li>
                        <li>{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_3' | translate }}</li>
                        <li>{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_4' | translate }}</li>
                        <li>{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_5' | translate }}</li>
                        <li>{{ 'LOGIN_PAGE.PASSWORD_RULES.RULE_6' | translate }}</li>
                      </ul>
                </div>
      </app-form-builder>
    </div>

    <!-- Action Buttons -->

    <div class="my-4 w-50 d-flex flex-column justify-content-center align-items-center w-50">
      <app-custom-button type="submit" class="w-100 change-password-btn"  [iconName]="" [btnName]="'LOGIN_PAGE.CHANGE_PASSWORD' | translate" (click)="onSubmit()"></app-custom-button>

    </div>


</div>
