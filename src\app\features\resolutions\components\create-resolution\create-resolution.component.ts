import { Component, OnInit ,inject} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { InputType } from '@shared/enum/input-type.enum';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';
import { BreadcrumbComponent } from '../../../../shared/components/breadcrumb/breadcrumb.component';
import { SizeEnum } from '@core/enums/size';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import {
  ResolutionsServiceProxy,
  AddResolutionCommand,
  VotingType,
  MemberVotingResult,
  ResolutionStatusEnum,
  TypesServiceProxy,
  FundsServiceProxy,
} from '@core/api/api.generated';
import { DateTime } from 'luxon';
import { ChangeDetectorRef } from '@angular/core';
import { ErrorModalService } from '@core/services/error-modal.service';
import moment from 'moment';
import { DateConversionService } from '@shared/services/date.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-create-resolution',
  standalone: true,
  imports: [
    CommonModule,
    FormBuilderComponent,
    PageHeaderComponent,
    TranslateModule,
    RouterModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CustomButtonComponent,
    BreadcrumbComponent,
  ],
  templateUrl: './create-resolution.component.html',
  styleUrls: ['./create-resolution.component.scss'],
})
export class CreateResolutionComponent implements OnInit {
  breadcrumbSizeEnum = SizeEnum;
  breadcrumbItems: IBreadcrumbItem[] = [];

  formGroup!: FormGroup;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  isFormSubmitted: boolean = false;
  isValidationFire: boolean = false;
  fundId!: number;
  fundName: string = '';
  isSubmitting = false;
  private translate = inject(TranslateService);


  // API data
  resolutionTypes: any[] = [];
  fundVoteType: any | null = null;

  // UI state
  showCustomTypeField: boolean = false;

  get visibleFormControls(): IControlOption[] {
    return this.formControls.filter((control) => {
      if (control.formControlName === 'customTypeName') {
        return this.showCustomTypeField;
      }
      return true;
    });
  }

  formControls: IControlOption[] = [
    {
      type: InputType.Date,
      formControlName: 'resolutionDate',
      id: 'resolutionDate',
      name: 'resolutionDate',
      label: 'INVESTMENT_FUNDS.RESOLUTIONS.DECISION_DATE',
      placeholder: 'INVESTMENT_FUNDS.RESOLUTIONS.ENTER_DECISION_DATE',
      isRequired: true,
      class: 'col-md-4',
    },
    {
      type: InputType.Dropdown,
      formControlName: 'typeId',
      id: 'typeId',
      name: 'typeId',
      label: 'RESOLUTIONS.TYPE',
      placeholder: 'RESOLUTIONS.TYPE_PLACEHOLDER',
      isRequired: true,
      class: 'col-md-4',
      options: [], // Will be populated from API
      onChange: (value: any) => this.dropdownChanged(value),
    },
    {
      type: InputType.Text,
      formControlName: 'customTypeName',
      id: 'customTypeName',
      name: 'customTypeName',
      label: 'RESOLUTIONS.CUSTOM_TYPE',
      placeholder: 'RESOLUTIONS.CUSTOM_TYPE_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-4',
      isVisible: () => this.showCustomTypeField,
      maxLength:100,
    },
    {
      type: InputType.Radio,
      formControlName: 'votingMethodologyId',
      id: 'votingMethodologyId',
      name: 'votingMethodologyId',
      label: 'RESOLUTIONS.VOTING_METHODOLOGY',
      isRequired: true,
      class: 'col-md-6',
      options: [
        { name: 'INVESTMENT_FUNDS.FORM.VOTING_ALL', id: 1 },
        { name: 'INVESTMENT_FUNDS.FORM.VOTING_MEMBERS', id: 2 },
      ],
    },

    {
      type: InputType.Radio,
      formControlName: 'votingResultCalculationId',
      id: 'votingResultCalculationId',
      name: 'votingResultCalculationId',
      label: 'RESOLUTIONS.VOTING_RESULT',
      isRequired: true,
      class: 'col-md-6',
      options: [
        { name: 'RESOLUTIONS.ALL_ITEMS', id: 1 },
        { name: 'RESOLUTIONS.MAJORITY_ITEMS', id: 2 },
      ],
    },
    {
      type: InputType.Textarea,
      formControlName: 'description',
      id: 'description',
      name: 'description',
      label: 'RESOLUTIONS.DESCRIPTION',
      placeholder: 'RESOLUTIONS.DESCRIPTION_PLACEHOLDER',
      isRequired: false,
      class: 'col-md-12',
      maxLength: 500,
    },
    {
      type: InputType.file,
      formControlName: 'attachmentId',
      id: 'attachmentId',
      name: 'attachmentId',
      label: 'RESOLUTIONS.FILE',
      placeholder: 'RESOLUTIONS.FILE_UPLOAD_TEXT',
      isRequired: true,
      class: 'col-md-12',
      allowedTypes: ['.pdf'],
    },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private resolutionsProxy: ResolutionsServiceProxy,
    private typesProxy: TypesServiceProxy,
    private fundProxy: FundsServiceProxy,
    private DateConversionService: DateConversionService,
    private changeDetectorRef: ChangeDetectorRef,
    private errorModalService: ErrorModalService
  ) {
    // Initialize breadcrumb with default values
    this.updateBreadcrumbWithFallback();
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      const fundIdParam = params['fundId'];
      this.fundId = parseInt(fundIdParam, 10);

      if (isNaN(this.fundId) || this.fundId <= 0) {
        this.fundId = Number(fundIdParam) || 0;
        if (this.fundId <= 0) {
          // Still update breadcrumb even if fundId is invalid, but with fallback URLs
          this.updateBreadcrumbWithFallback();
          console.warn('Invalid fundId provided:', fundIdParam);
          return;
        }
      }
      this.initializeForm();

      this.updateBreadcrumb();
      this.loadResolutionTypes();
      this.loadSelectedFundVoteType();
    });
  }

  private initializeForm() {
    const formGroup: any = {};

    this.formControls.forEach((control) => {
      const validators = [];
      if (control.formControlName == '') return;
      if (control.isRequired) {
        validators.push(Validators.required);
      }
      if (control.minLength) {
        validators.push(Validators.minLength(control.minLength));
      }
      if (control.maxLength) {
        validators.push(Validators.maxLength(control.maxLength));
      }
      if (control.max) {
        validators.push(Validators.max(control.max));
      }
      if (control.min) {
        validators.push(Validators.min(control.min));
      }

      formGroup[control.formControlName] = [null, validators];
    });
    this.formGroup = this.formBuilder.group(formGroup);
  }

  private updateBreadcrumb(): void {
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      {
        label: 'BREADCRUMB.FUND_DETAILS',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: `/admin/investment-funds/resolutions?fundId=${this.fundId}`,
      },
      { label: 'RESOLUTIONS.CREATE_TITLE', url: '', disabled: true },
    ];
  }

  private updateBreadcrumbWithFallback(): void {
    // Fallback breadcrumb when fundId is not available
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      {
        label: 'BREADCRUMB.FUND_DETAILS',
        url: '/admin/investment-funds',
        disabled: true,
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: '/admin/investment-funds',
        disabled: true,
      },
      { label: 'RESOLUTIONS.CREATE_TITLE', url: '', disabled: true },
    ];
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    console.log('Breadcrumb clicked:', item);

    if (!item) {
      console.warn('Breadcrumb item is null or undefined');
      return;
    }

    if (item.disabled) {
      console.log('Breadcrumb item is disabled, ignoring click');
      return;
    }

    if (!item.url) {
      console.warn('Breadcrumb item has no URL:', item);
      return;
    }

    console.log('Navigating to:', item.url);
    this.router.navigateByUrl(item.url).catch((error) => {
      console.error('Navigation failed:', error);
    });
  }

  dropdownChanged(event: any): void {
    const selectedId = event?.id || event?.event?.id;

    const customTypeNameControl = this.formControls.find((control) => control.formControlName === 'customTypeName');
    if (selectedId === 10) {
      // Show custom type field when "Other" is selected
      if (customTypeNameControl) {
        customTypeNameControl.isRequired = true;
      }
      this.formGroup.get('customTypeName')?.setValidators([Validators.required, Validators.maxLength(100)]);      // Show custom type field when "Other" is selected
      this.showCustomTypeField = true;
    } else {
      // Hide custom type field for other selections
      if (customTypeNameControl) {
        customTypeNameControl.isRequired = false;
      }
      this.showCustomTypeField = false;
      this.formGroup.get('customTypeName')?.clearValidators();
      this.formGroup.get('customTypeName')?.setValue('');
    }
    this.formGroup.get('customTypeName')?.updateValueAndValidity();  

    // Trigger change detection
    this.changeDetectorRef.detectChanges();
  }

  onFileUpload(event: any): void {
    // Mock file upload - replace with actual implementation
    debugger;
    if (event.file) {
      // Simulate upload
      setTimeout(() => {
        this.formGroup.patchValue({ attachmentId: event.file.id });
      }, 1000);
    }
  }

  // Add method to handle date selection
  dateSelected(event: any): void {
    this.formGroup
      .get(event.control.formControlName)
      ?.setValue(event.event.formattedGregorian);
  }

  onSubmit(saveAsDraft: boolean = false): void {
    if (this.isSubmitting) return;
    this.isSubmitting = true;

    this.isFormSubmitted = true;
    this.isValidationFire = true;

    if (!this.isFormValid(saveAsDraft)) {
      this.isSubmitting = false;
      return;
    }

    const formValue = this.formGroup.value;
    const command = new AddResolutionCommand();

    command.id = 0;
    command.code = undefined;

    // Fix date handling - use the recommended pattern and ensure proper format
    const resolutionDateValue =
      this.formGroup.get('resolutionDate')?.value ?? '';
    if (resolutionDateValue) {
      // If it's already a DateTime object, use it directly
      if (resolutionDateValue instanceof DateTime) {
        command.resolutionDate = resolutionDateValue;
      } else if (typeof resolutionDateValue === 'string') {
        // If it's a string, try to parse it as ISO
        command.resolutionDate = DateTime.fromISO(resolutionDateValue);
      } else if (resolutionDateValue instanceof Date) {
        // If it's a JavaScript Date object, convert to DateTime
        command.resolutionDate = DateTime.fromJSDate(resolutionDateValue);
      } else {
        // Fallback: try to create DateTime from the value
        command.resolutionDate = DateTime.fromISO(
          resolutionDateValue.toString()
        );
      }

      // Ensure the DateTime is valid
      if (!command.resolutionDate.isValid) {
        this.errorModalService.showError('RESOLUTIONS.ERROR_DATE_INVALID');
        this.isSubmitting = false;
        return;
      }
    } else {
      this.errorModalService.showError('RESOLUTIONS.ERROR_DATE_REQUIRED');
      this.isSubmitting = false;
      return;
    }

    command.description = formValue.description;
    command.resolutionTypeId = formValue.typeId;
    command.newType = formValue.customTypeName;
    command.attachmentId = formValue.attachmentId;
    command.votingType =
      formValue.votingMethodologyId === 1 ? VotingType._1 : VotingType._2;
    command.memberVotingResult =
      formValue.votingResultCalculationId === 1
        ? MemberVotingResult._1
        : MemberVotingResult._2;
    command.status = saveAsDraft
      ? ResolutionStatusEnum._1
      : ResolutionStatusEnum._2;
    command.fundId = this.fundId;
    command.parentResolutionId = undefined;
    command.oldResolutionCode = undefined;
    command.saveAsDraft = saveAsDraft;
    command.originalResolutionId = undefined;

    if (this.formGroup.valid) {
      this.resolutionsProxy.addResolution(command).subscribe({
        next: (response) => {
          this.isSubmitting = false;
          if (response.successed) {
            const message = this.translate.instant('INVESTMENT_FUNDS.RESOLUTIONS.SUCCESS_SAVED')    
            this.errorModalService.showSuccess(message);
            this.router.navigate(['/admin/investment-funds/resolutions'], {
              queryParams: { fundId: this.fundId },
            });
          } else {
            this.errorModalService.showError(
              response.message || this.translate.instant('RESOLUTIONS.CREATE_ERROR')
            );
          }
        },
        error: (error) => {
          this.isSubmitting = false;
          console.error('Error creating resolution:', error);
          this.errorModalService.showError(this.translate.instant('RESOLUTIONS.CREATE_ERROR' ));
        },
      });
    } else {
      this.isSubmitting = false;
      this.formGroup.markAllAsTouched();
    }
  }

  isFormValid(saveAsDraft: boolean): boolean {
    if (saveAsDraft) {
      // For draft, only date and type are required
      const hasDate = !!this.formGroup.value.resolutionDate;
      const hasType = !!this.formGroup.value.typeId;
      return hasDate && hasType;
    }

    // For sending, all fields are required
    return this.formGroup.valid && this.formGroup.value.attachmentId > 0;
  }

  onCancel(): void {
    this.router.navigate([`/admin/investment-funds/resolutions`], {
      queryParams: { fundId: this.fundId },
    });
  }

  private loadResolutionTypes(): void {

    this.typesProxy.all().subscribe({
      next: (response) => {
        if (response.successed && response.data) {
          this.resolutionTypes = response.data;
          this.updateResolutionTypesOptions();
        }
      },
      error: () => {
        // Show Error on Fallback

      },
    });
  }
  private loadSelectedFundVoteType(): void {
    if (isNaN(this.fundId) || this.fundId <= 0) {
      return;
    }

    this.fundProxy.basicInfo(this.fundId).subscribe({
      next: (response) => {
        debugger;
        if (response.successed && response.data) {
          if (response.data.initiationDate)
          this.setDateRange(response.data.initiationDate);

          this.formGroup.patchValue({
            votingMethodologyId: response.data.votingType.typeId,
            votingResultCalculationId: 2,
          });
        }
      },
      error: () => {
        // Keep the default options as fallback
      },
    });
  }
  setDateRange(date: any) {
    moment.locale('en');
    const minDate = this.DateConversionService.mapStringToSelectedDate(
      moment(date.toJSDate()).format('DD-MM-YYYY')
    );

    const maxDate = this.DateConversionService.mapStringToSelectedDate(
      moment().format('DD-MM-YYYY')
    );

    const field = this.formControls.find(
      (f) => f.formControlName === 'resolutionDate'
    );
    if (field) {
      field.minGreg = minDate;
      field.minHijri = field.minGreg
        ? this.DateConversionService.convertGregorianToHijri(field.minGreg)
        : undefined;
      field.maxGreg = maxDate;
      field.maxHijri = field.maxGreg
        ? this.DateConversionService.convertGregorianToHijri(field.maxGreg)
        : undefined;
    }

    // Mock fund vote type - replace with actual API call when available
    this.fundVoteType = {
      votingType: VotingType._1, // All members
      memberVotingResult: MemberVotingResult._1, // All items
    };
    this.updateVotingMethodologyOptions();
  }

  private updateResolutionTypesOptions(): void {
    const typeControl = this.formControls.find(
      (control) => control.formControlName === 'typeId'
    );
    if (typeControl) {
      typeControl.options = this.resolutionTypes.map((type) => ({
        name: type.localizedName || type.nameAr || type.nameEn || '',
        id: type.id,
      }));
    }
  }

  private updateVotingMethodologyOptions(): void {
    if (this.fundVoteType) {
      // Only auto-select the fund's default voting type for votingMethodologyId
      // Keep votingResultCalculationId with its default value (2 = Majority of items)
      this.formGroup.patchValue({
        votingMethodologyId: this.fundVoteType.votingTypeId,
        votingResultCalculationId: 2,
        // Don't change votingResultCalculationId - it should keep its default value
      });
    }
  }
}
